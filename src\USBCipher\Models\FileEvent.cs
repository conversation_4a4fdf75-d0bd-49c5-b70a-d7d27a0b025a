using System;

namespace USBCipher.Models;

public enum FileEventType
{
    Created,
    Modified,
    Deleted,
    Renamed
}

public class FileEvent
{
    public int Id { get; set; }
    public FileEventType EventType { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string? OldFilePath { get; set; }
    public DateTime Timestamp { get; set; }
    public string Username { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileType { get; set; } = string.Empty;
}
