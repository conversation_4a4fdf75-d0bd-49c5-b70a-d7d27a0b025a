using System;
using System.IO;
using USBCipher.Services;
using Xunit;

namespace USBCipher.Tests;

public class SensitiveDataDetectorTests
{
    private readonly string _testFilesDir;
    
    public SensitiveDataDetectorTests()
    {
        // Create a temporary directory for test files
        _testFilesDir = Path.Combine(Path.GetTempPath(), "USBCipherTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testFilesDir);
    }
    
    [Fact]
    public void AnalyzeFile_WithSensitiveKeyword_ReturnsTrue()
    {
        // Arrange
        var detector = new SensitiveDataDetector();
        var filePath = Path.Combine(_testFilesDir, "sensitive.txt");
        File.WriteAllText(filePath, "This file contains confidential information.");
        
        // Act
        var result = detector.AnalyzeFile(filePath);
        
        // Assert
        Assert.True(result.ContainsSensitiveData);
        Assert.Contains("keyword", result.DetectionReason, StringComparison.OrdinalIgnoreCase);
    }
    
    [Fact]
    public void AnalyzeFile_WithSSN_ReturnsTrue()
    {
        // Arrange
        var detector = new SensitiveDataDetector();
        var filePath = Path.Combine(_testFilesDir, "ssn.txt");
        File.WriteAllText(filePath, "SSN: ***********");
        
        // Act
        var result = detector.AnalyzeFile(filePath);
        
        // Assert
        Assert.True(result.ContainsSensitiveData);
        Assert.Contains("Social Security Number", result.DetectionReason, StringComparison.OrdinalIgnoreCase);
    }
    
    [Fact]
    public void AnalyzeFile_WithCreditCard_ReturnsTrue()
    {
        // Arrange
        var detector = new SensitiveDataDetector();
        var filePath = Path.Combine(_testFilesDir, "creditcard.txt");
        File.WriteAllText(filePath, "Credit Card: 1234-5678-9012-3456");
        
        // Act
        var result = detector.AnalyzeFile(filePath);
        
        // Assert
        Assert.True(result.ContainsSensitiveData);
        Assert.Contains("credit card", result.DetectionReason, StringComparison.OrdinalIgnoreCase);
    }
    
    [Fact]
    public void AnalyzeFile_WithWatchedExtension_ReturnsTrue()
    {
        // Arrange
        var detector = new SensitiveDataDetector();
        var filePath = Path.Combine(_testFilesDir, "document.docx");
        File.WriteAllText(filePath, "This is a normal document.");
        
        // Act
        var result = detector.AnalyzeFile(filePath);
        
        // Assert
        Assert.True(result.ContainsSensitiveData);
        Assert.Contains("extension", result.DetectionReason, StringComparison.OrdinalIgnoreCase);
    }
    
    [Fact]
    public void AnalyzeFile_WithNormalContent_ReturnsFalse()
    {
        // Arrange
        var detector = new SensitiveDataDetector();
        var filePath = Path.Combine(_testFilesDir, "normal.txt");
        File.WriteAllText(filePath, "This is a normal file with no sensitive information.");
        
        // Act
        var result = detector.AnalyzeFile(filePath);
        
        // Assert
        Assert.False(result.ContainsSensitiveData);
        Assert.Equal(string.Empty, result.DetectionReason);
    }
    
    [Fact]
    public void AnalyzeFile_WithCustomKeywords_ReturnsTrue()
    {
        // Arrange
        var customKeywords = new List<string> { "topsecret", "classified" };
        var detector = new SensitiveDataDetector(customKeywords);
        var filePath = Path.Combine(_testFilesDir, "custom.txt");
        File.WriteAllText(filePath, "This file is topsecret.");
        
        // Act
        var result = detector.AnalyzeFile(filePath);
        
        // Assert
        Assert.True(result.ContainsSensitiveData);
        Assert.Contains("topsecret", result.DetectionReason, StringComparison.OrdinalIgnoreCase);
    }
    
    public void Dispose()
    {
        // Clean up test files
        if (Directory.Exists(_testFilesDir))
        {
            Directory.Delete(_testFilesDir, true);
        }
    }
}
