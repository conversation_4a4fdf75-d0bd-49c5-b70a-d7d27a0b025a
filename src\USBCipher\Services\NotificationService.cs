using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using MailKit.Net.Smtp;
using MimeKit;

namespace USBCipher.Services;

/// <summary>
/// Service for sending notifications and alerts
/// </summary>
public class NotificationService
{
    private readonly string? _smtpServer;
    private readonly int _smtpPort;
    private readonly string? _smtpUsername;
    private readonly string? _smtpPassword;
    private readonly string? _fromEmail;
    private readonly string? _toEmail;
    private readonly string? _slackWebhookUrl;

    public NotificationService(
        string? smtpServer = null,
        int smtpPort = 587,
        string? smtpUsername = null,
        string? smtpPassword = null,
        string? fromEmail = null,
        string? toEmail = null,
        string? slackWebhookUrl = null)
    {
        _smtpServer = smtpServer;
        _smtpPort = smtpPort;
        _smtpUsername = smtpUsername;
        _smtpPassword = smtpPassword;
        _fromEmail = fromEmail;
        _toEmail = toEmail;
        _slackWebhookUrl = slackWebhookUrl;
    }

    public async Task SendAlert(string subject, string body)
    {
        // Send email alert
        if (!string.IsNullOrEmpty(_smtpServer) && 
            !string.IsNullOrEmpty(_fromEmail) && 
            !string.IsNullOrEmpty(_toEmail))
        {
            await SendEmailAsync(subject, body);
        }
        
        // Send Slack alert
        if (!string.IsNullOrEmpty(_slackWebhookUrl))
        {
            await SendSlackMessageAsync(subject, body);
        }
        
        // Log the alert
        Console.WriteLine($"Alert: {subject}\n{body}");
    }

    private async Task SendEmailAsync(string subject, string body)
    {
        try
        {
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("USBCipher", _fromEmail));
            message.To.Add(new MailboxAddress("Administrator", _toEmail));
            message.Subject = subject;
            
            var bodyBuilder = new BodyBuilder
            {
                TextBody = body
            };
            
            message.Body = bodyBuilder.ToMessageBody();
            
            using var client = new SmtpClient();
            await client.ConnectAsync(_smtpServer, _smtpPort, false);
            
            if (!string.IsNullOrEmpty(_smtpUsername) && !string.IsNullOrEmpty(_smtpPassword))
            {
                await client.AuthenticateAsync(_smtpUsername, _smtpPassword);
            }
            
            await client.SendAsync(message);
            await client.DisconnectAsync(true);
            
            Console.WriteLine("Email alert sent successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending email alert: {ex.Message}");
        }
    }

    private async Task SendSlackMessageAsync(string subject, string body)
    {
        try
        {
            using var httpClient = new HttpClient();
            
            var payload = new
            {
                text = $"*{subject}*\n```{body}```"
            };
            
            var json = System.Text.Json.JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await httpClient.PostAsync(_slackWebhookUrl, content);
            
            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("Slack alert sent successfully");
            }
            else
            {
                Console.WriteLine($"Error sending Slack alert: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending Slack alert: {ex.Message}");
        }
    }

    public async Task SendWeeklyReport(string reportPath, string subject, string body)
    {
        try
        {
            if (string.IsNullOrEmpty(_smtpServer) || 
                string.IsNullOrEmpty(_fromEmail) || 
                string.IsNullOrEmpty(_toEmail))
            {
                Console.WriteLine("Email configuration is incomplete. Cannot send weekly report.");
                return;
            }
            
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("USBCipher", _fromEmail));
            message.To.Add(new MailboxAddress("Administrator", _toEmail));
            message.Subject = subject;
            
            var bodyBuilder = new BodyBuilder
            {
                TextBody = body
            };
            
            // Attach the report file
            if (System.IO.File.Exists(reportPath))
            {
                bodyBuilder.Attachments.Add(reportPath);
            }
            
            message.Body = bodyBuilder.ToMessageBody();
            
            using var client = new SmtpClient();
            await client.ConnectAsync(_smtpServer, _smtpPort, false);
            
            if (!string.IsNullOrEmpty(_smtpUsername) && !string.IsNullOrEmpty(_smtpPassword))
            {
                await client.AuthenticateAsync(_smtpUsername, _smtpPassword);
            }
            
            await client.SendAsync(message);
            await client.DisconnectAsync(true);
            
            Console.WriteLine("Weekly report sent successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending weekly report: {ex.Message}");
        }
    }
}
