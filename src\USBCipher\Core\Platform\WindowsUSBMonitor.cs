using System;
using System.Management;
using System.Runtime.InteropServices;

namespace USBCipher.Core.Platform;

/// <summary>
/// Windows-specific implementation for USB device monitoring
/// </summary>
public class WindowsUSBMonitor
{
    private readonly Action<string> _onDeviceConnected;
    private readonly Action<string> _onDeviceDisconnected;
    private ManagementEventWatcher? _insertWatcher;
    private ManagementEventWatcher? _removeWatcher;

    public WindowsUSBMonitor(Action<string> onDeviceConnected, Action<string> onDeviceDisconnected)
    {
        _onDeviceConnected = onDeviceConnected;
        _onDeviceDisconnected = onDeviceDisconnected;
    }

    public void Start()
    {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            throw new PlatformNotSupportedException("Windows USB monitor can only be used on Windows.");
        }

        try
        {
            // WMI query for USB drive insertion
            var insertQuery = new WqlEventQuery(
                "SELECT * FROM Win32_VolumeChangeEvent WHERE EventType = 2");
            
            _insertWatcher = new ManagementEventWatcher(insertQuery);
            _insertWatcher.EventArrived += OnDriveInserted;
            _insertWatcher.Start();
            
            // WMI query for USB drive removal
            var removeQuery = new WqlEventQuery(
                "SELECT * FROM Win32_VolumeChangeEvent WHERE EventType = 3");
            
            _removeWatcher = new ManagementEventWatcher(removeQuery);
            _removeWatcher.EventArrived += OnDriveRemoved;
            _removeWatcher.Start();
            
            Console.WriteLine("Windows USB monitoring started");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting Windows USB monitoring: {ex.Message}");
            throw;
        }
    }

    public void Stop()
    {
        try
        {
            if (_insertWatcher != null)
            {
                _insertWatcher.EventArrived -= OnDriveInserted;
                _insertWatcher.Stop();
                _insertWatcher.Dispose();
                _insertWatcher = null;
            }
            
            if (_removeWatcher != null)
            {
                _removeWatcher.EventArrived -= OnDriveRemoved;
                _removeWatcher.Stop();
                _removeWatcher.Dispose();
                _removeWatcher = null;
            }
            
            Console.WriteLine("Windows USB monitoring stopped");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error stopping Windows USB monitoring: {ex.Message}");
        }
    }

    private void OnDriveInserted(object sender, EventArrivedEventArgs e)
    {
        try
        {
            var driveName = e.NewEvent.Properties["DriveName"].Value.ToString();
            
            if (!string.IsNullOrEmpty(driveName))
            {
                _onDeviceConnected(driveName);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling drive insertion: {ex.Message}");
        }
    }

    private void OnDriveRemoved(object sender, EventArrivedEventArgs e)
    {
        try
        {
            var driveName = e.NewEvent.Properties["DriveName"].Value.ToString();
            
            if (!string.IsNullOrEmpty(driveName))
            {
                _onDeviceDisconnected(driveName);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling drive removal: {ex.Message}");
        }
    }
}
