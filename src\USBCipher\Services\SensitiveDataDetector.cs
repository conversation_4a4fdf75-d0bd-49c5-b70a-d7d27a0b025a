using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using USBCipher.Models;

namespace USBCipher.Services;

/// <summary>
/// Service for detecting sensitive data in files
/// </summary>
public class SensitiveDataDetector
{
    private readonly List<string> _sensitiveKeywords;
    private readonly List<string> _watchedFileExtensions;
    
    // Regular expressions for common sensitive data patterns
    private static readonly Regex SsnRegex = new(@"\b\d{3}-\d{2}-\d{4}\b", RegexOptions.Compiled);
    private static readonly Regex CreditCardRegex = new(@"\b(?:\d{4}[-\s]?){3}\d{4}\b", RegexOptions.Compiled);
    private static readonly Regex EmailRegex = new(@"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", RegexOptions.Compiled);

    public SensitiveDataDetector(List<string>? sensitiveKeywords = null, List<string>? watchedFileExtensions = null)
    {
        _sensitiveKeywords = sensitiveKeywords ?? new List<string>
        {
            "confidential",
            "secret",
            "private",
            "sensitive",
            "classified",
            "restricted",
            "internal",
            "proprietary",
            "client",
            "customer",
            "password",
            "credential"
        };
        
        _watchedFileExtensions = watchedFileExtensions ?? new List<string>
        {
            ".docx",
            ".xlsx",
            ".pdf",
            ".zip",
            ".txt",
            ".csv",
            ".json",
            ".xml"
        };
    }

    public SensitiveDataResult AnalyzeFile(string filePath)
    {
        var result = new SensitiveDataResult
        {
            ContainsSensitiveData = false,
            DetectionReason = string.Empty
        };
        
        // Check file extension
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        if (_watchedFileExtensions.Contains(extension))
        {
            result.ContainsSensitiveData = true;
            result.DetectionReason = $"Watched file extension: {extension}";
            return result;
        }
        
        // Skip binary files or large files
        var fileInfo = new FileInfo(filePath);
        if (fileInfo.Length > 10 * 1024 * 1024) // Skip files larger than 10MB
        {
            return result;
        }
        
        try
        {
            // Read file content
            string content;
            using (var reader = new StreamReader(filePath))
            {
                content = reader.ReadToEnd();
            }
            
            // Check for sensitive keywords
            foreach (var keyword in _sensitiveKeywords)
            {
                if (content.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    result.ContainsSensitiveData = true;
                    result.DetectionReason = $"Contains sensitive keyword: {keyword}";
                    return result;
                }
            }
            
            // Check for SSNs
            if (SsnRegex.IsMatch(content))
            {
                result.ContainsSensitiveData = true;
                result.DetectionReason = "Contains Social Security Number pattern";
                return result;
            }
            
            // Check for credit card numbers
            if (CreditCardRegex.IsMatch(content))
            {
                result.ContainsSensitiveData = true;
                result.DetectionReason = "Contains credit card number pattern";
                return result;
            }
            
            // Check for email addresses (optional, depending on policy)
            if (EmailRegex.IsMatch(content))
            {
                // This could be a configurable option
                // result.ContainsSensitiveData = true;
                // result.DetectionReason = "Contains email addresses";
                // return result;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error analyzing file {filePath}: {ex.Message}");
        }
        
        return result;
    }
}
