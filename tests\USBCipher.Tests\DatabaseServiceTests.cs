using System;
using System.IO;
using USBCipher.Models;
using USBCipher.Services;
using Xunit;

namespace USBCipher.Tests;

public class DatabaseServiceTests : IDisposable
{
    private readonly string _databasePath;
    private readonly DatabaseService _databaseService;
    
    public DatabaseServiceTests()
    {
        // Create a temporary database file
        _databasePath = Path.Combine(Path.GetTempPath(), $"USBCipherTests_{Guid.NewGuid()}.db");
        _databaseService = new DatabaseService(_databasePath);
    }
    
    [Fact]
    public void LogUSBEvent_ShouldAddEventToDatabase()
    {
        // Arrange
        var usbEvent = new USBEvent
        {
            EventType = USBEventType.Connected,
            DrivePath = "E:\\",
            Timestamp = DateTime.Now,
            Username = "TestUser"
        };
        
        // Act
        _databaseService.LogUSBEvent(usbEvent);
        
        // Assert
        // We can't directly assert the database contents in this test,
        // but we can verify that no exception was thrown
        // A more comprehensive test would query the database to verify the event was added
    }
    
    [Fact]
    public void LogFileEvent_ShouldAddEventToDatabase()
    {
        // Arrange
        var fileEvent = new FileEvent
        {
            EventType = FileEventType.Created,
            FilePath = "E:\\test.txt",
            Timestamp = DateTime.Now,
            Username = "TestUser",
            FileSize = 1024,
            FileType = ".txt"
        };
        
        // Act
        _databaseService.LogFileEvent(fileEvent);
        
        // Assert
        // We can't directly assert the database contents in this test,
        // but we can verify that no exception was thrown
        // A more comprehensive test would query the database to verify the event was added
    }
    
    [Fact]
    public void GetRecentFileEvents_ShouldReturnEvents()
    {
        // Arrange
        var fileEvent1 = new FileEvent
        {
            EventType = FileEventType.Created,
            FilePath = "E:\\test1.txt",
            Timestamp = DateTime.Now,
            Username = "TestUser",
            FileSize = 1024,
            FileType = ".txt"
        };
        
        var fileEvent2 = new FileEvent
        {
            EventType = FileEventType.Modified,
            FilePath = "E:\\test2.txt",
            Timestamp = DateTime.Now.AddMinutes(-1),
            Username = "TestUser",
            FileSize = 2048,
            FileType = ".txt"
        };
        
        _databaseService.LogFileEvent(fileEvent1);
        _databaseService.LogFileEvent(fileEvent2);
        
        // Act
        var events = _databaseService.GetRecentFileEvents(10);
        
        // Assert
        Assert.Equal(2, events.Count);
        Assert.Contains(events, e => e.FilePath == "E:\\test1.txt");
        Assert.Contains(events, e => e.FilePath == "E:\\test2.txt");
    }
    
    public void Dispose()
    {
        // Clean up the test database
        if (File.Exists(_databasePath))
        {
            File.Delete(_databasePath);
        }
    }
}
