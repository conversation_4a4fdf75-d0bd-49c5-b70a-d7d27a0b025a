using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.Data.Sqlite;
using USBCipher.Models;

namespace USBCipher.Services;

/// <summary>
/// Service for managing the SQLite database
/// </summary>
public class DatabaseService
{
    private readonly string _databasePath;
    private readonly string _connectionString;

    public DatabaseService(string databasePath)
    {
        _databasePath = databasePath;
        _connectionString = $"Data Source={_databasePath}";
        
        // Ensure the directory exists
        Directory.CreateDirectory(Path.GetDirectoryName(_databasePath) ?? string.Empty);
        
        // Initialize the database
        InitializeDatabase();
    }

    private void InitializeDatabase()
    {
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        // Create USB events table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                CREATE TABLE IF NOT EXISTS USBEvents (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    EventType INTEGER NOT NULL,
                    DrivePath TEXT NOT NULL,
                    Timestamp TEXT NOT NULL,
                    Username TEXT NOT NULL
                )";
            command.ExecuteNonQuery();
        }
        
        // Create file events table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                CREATE TABLE IF NOT EXISTS FileEvents (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    EventType INTEGER NOT NULL,
                    FilePath TEXT NOT NULL,
                    OldFilePath TEXT,
                    Timestamp TEXT NOT NULL,
                    Username TEXT NOT NULL,
                    FileSize INTEGER NOT NULL,
                    FileType TEXT NOT NULL
                )";
            command.ExecuteNonQuery();
        }
        
        // Create quarantined files table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                CREATE TABLE IF NOT EXISTS QuarantinedFiles (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    OriginalFilePath TEXT NOT NULL,
                    QuarantinePath TEXT NOT NULL,
                    Timestamp TEXT NOT NULL,
                    Username TEXT NOT NULL,
                    DetectionReason TEXT NOT NULL
                )";
            command.ExecuteNonQuery();
        }
    }

    public void LogUSBEvent(USBEvent usbEvent)
    {
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        using var command = connection.CreateCommand();
        command.CommandText = @"
            INSERT INTO USBEvents (EventType, DrivePath, Timestamp, Username)
            VALUES (@EventType, @DrivePath, @Timestamp, @Username)";
        
        command.Parameters.AddWithValue("@EventType", (int)usbEvent.EventType);
        command.Parameters.AddWithValue("@DrivePath", usbEvent.DrivePath);
        command.Parameters.AddWithValue("@Timestamp", usbEvent.Timestamp.ToString("o"));
        command.Parameters.AddWithValue("@Username", usbEvent.Username);
        
        command.ExecuteNonQuery();
    }

    public void LogFileEvent(FileEvent fileEvent)
    {
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        using var command = connection.CreateCommand();
        command.CommandText = @"
            INSERT INTO FileEvents (EventType, FilePath, OldFilePath, Timestamp, Username, FileSize, FileType)
            VALUES (@EventType, @FilePath, @OldFilePath, @Timestamp, @Username, @FileSize, @FileType)";
        
        command.Parameters.AddWithValue("@EventType", (int)fileEvent.EventType);
        command.Parameters.AddWithValue("@FilePath", fileEvent.FilePath);
        command.Parameters.AddWithValue("@OldFilePath", fileEvent.OldFilePath as object ?? DBNull.Value);
        command.Parameters.AddWithValue("@Timestamp", fileEvent.Timestamp.ToString("o"));
        command.Parameters.AddWithValue("@Username", fileEvent.Username);
        command.Parameters.AddWithValue("@FileSize", fileEvent.FileSize);
        command.Parameters.AddWithValue("@FileType", fileEvent.FileType);
        
        command.ExecuteNonQuery();
    }

    public void LogQuarantinedFile(string originalFilePath, string quarantinePath, string username, string detectionReason)
    {
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        using var command = connection.CreateCommand();
        command.CommandText = @"
            INSERT INTO QuarantinedFiles (OriginalFilePath, QuarantinePath, Timestamp, Username, DetectionReason)
            VALUES (@OriginalFilePath, @QuarantinePath, @Timestamp, @Username, @DetectionReason)";
        
        command.Parameters.AddWithValue("@OriginalFilePath", originalFilePath);
        command.Parameters.AddWithValue("@QuarantinePath", quarantinePath);
        command.Parameters.AddWithValue("@Timestamp", DateTime.Now.ToString("o"));
        command.Parameters.AddWithValue("@Username", username);
        command.Parameters.AddWithValue("@DetectionReason", detectionReason);
        
        command.ExecuteNonQuery();
    }

    public List<FileEvent> GetRecentFileEvents(int count = 50)
    {
        var events = new List<FileEvent>();
        
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        using var command = connection.CreateCommand();
        command.CommandText = @"
            SELECT Id, EventType, FilePath, OldFilePath, Timestamp, Username, FileSize, FileType
            FROM FileEvents
            ORDER BY Timestamp DESC
            LIMIT @Count";
        
        command.Parameters.AddWithValue("@Count", count);
        
        using var reader = command.ExecuteReader();
        while (reader.Read())
        {
            events.Add(new FileEvent
            {
                Id = reader.GetInt32(0),
                EventType = (FileEventType)reader.GetInt32(1),
                FilePath = reader.GetString(2),
                OldFilePath = reader.IsDBNull(3) ? null : reader.GetString(3),
                Timestamp = DateTime.Parse(reader.GetString(4)),
                Username = reader.GetString(5),
                FileSize = reader.GetInt64(6),
                FileType = reader.GetString(7)
            });
        }
        
        return events;
    }
}
