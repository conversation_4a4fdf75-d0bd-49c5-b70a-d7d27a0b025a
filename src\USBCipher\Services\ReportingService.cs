using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace USBCipher.Services;

/// <summary>
/// Service for generating reports
/// </summary>
public class ReportingService
{
    private readonly string _connectionString;
    private readonly string _reportOutputPath;
    private readonly NotificationService _notificationService;

    public ReportingService(string databasePath, string reportOutputPath, NotificationService notificationService)
    {
        _connectionString = $"Data Source={databasePath}";
        _reportOutputPath = reportOutputPath;
        _notificationService = notificationService;
        
        // Ensure the report output directory exists
        Directory.CreateDirectory(_reportOutputPath);
    }

    public async Task GenerateWeeklyReport()
    {
        try
        {
            var startDate = DateTime.Now.AddDays(-7);
            var endDate = DateTime.Now;
            
            // Generate report filename
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            var csvFilePath = Path.Combine(_reportOutputPath, $"USBCipher_Report_{timestamp}.csv");
            var pdfFilePath = Path.Combine(_reportOutputPath, $"USBCipher_Report_{timestamp}.pdf");
            
            // Generate CSV report
            GenerateCsvReport(startDate, endDate, csvFilePath);
            
            // Generate PDF report
            GeneratePdfReport(startDate, endDate, pdfFilePath);
            
            // Send email with the reports
            var subject = $"USBCipher Weekly Report - {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}";
            var body = $"Please find attached the weekly USB activity report for the period {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}.";
            
            await _notificationService.SendWeeklyReport(pdfFilePath, subject, body);
            
            Console.WriteLine($"Weekly report generated successfully: {pdfFilePath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating weekly report: {ex.Message}");
        }
    }

    private void GenerateCsvReport(DateTime startDate, DateTime endDate, string outputPath)
    {
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        var csvBuilder = new StringBuilder();
        
        // Add header
        csvBuilder.AppendLine("Report Type,Data");
        
        // Get USB events summary
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*) 
                FROM USBEvents 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate 
                AND EventType = 0"; // Connected events
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            var usbConnections = (long)command.ExecuteScalar();
            csvBuilder.AppendLine($"USB Connections,{usbConnections}");
        }
        
        // Get file events summary
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT EventType, COUNT(*) 
                FROM FileEvents 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate 
                GROUP BY EventType";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var eventType = reader.GetInt32(0);
                var count = reader.GetInt64(1);
                var eventTypeName = eventType switch
                {
                    0 => "Files Created",
                    1 => "Files Modified",
                    2 => "Files Deleted",
                    3 => "Files Renamed",
                    _ => $"Unknown Event Type ({eventType})"
                };
                
                csvBuilder.AppendLine($"{eventTypeName},{count}");
            }
        }
        
        // Get quarantined files summary
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*) 
                FROM QuarantinedFiles 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            var quarantinedFiles = (long)command.ExecuteScalar();
            csvBuilder.AppendLine($"Quarantined Files,{quarantinedFiles}");
        }
        
        // Get user activity summary
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT Username, COUNT(*) 
                FROM FileEvents 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate 
                GROUP BY Username";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var username = reader.GetString(0);
                var count = reader.GetInt64(1);
                
                csvBuilder.AppendLine($"User Activity: {username},{count}");
            }
        }
        
        // Write the CSV file
        File.WriteAllText(outputPath, csvBuilder.ToString());
    }

    private void GeneratePdfReport(DateTime startDate, DateTime endDate, string outputPath)
    {
        // Get report data
        var reportData = GetReportData(startDate, endDate);
        
        // Generate PDF using QuestPDF
        Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(10));
                
                page.Header().Element(ComposeHeader);
                page.Content().Element(container => ComposeContent(container, reportData, startDate, endDate));
                page.Footer().AlignCenter().Text(text =>
                {
                    text.Span("Page ");
                    text.CurrentPageNumber();
                    text.Span(" of ");
                    text.TotalPages();
                });
            });
        })
        .GeneratePdf(outputPath);
    }

    private void ComposeHeader(IContainer container)
    {
        container.Row(row =>
        {
            row.RelativeItem().Column(column =>
            {
                column.Item().Text("USBCipher Weekly Report")
                    .FontSize(20)
                    .SemiBold();
                
                column.Item().Text(text =>
                {
                    text.Span("Generated: ").SemiBold();
                    text.Span($"{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                });
            });
        });
    }

    private void ComposeContent(IContainer container, Dictionary<string, object> reportData, DateTime startDate, DateTime endDate)
    {
        container.Column(column =>
        {
            column.Item().Text($"Report Period: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}")
                .FontSize(12)
                .SemiBold();
            
            column.Spacing(10);
            
            // USB Events Summary
            column.Item().Text("USB Activity Summary").FontSize(14).SemiBold();
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(3);
                    columns.RelativeColumn(2);
                });
                
                table.Header(header =>
                {
                    header.Cell().Text("Event Type").SemiBold();
                    header.Cell().Text("Count").SemiBold();
                });
                
                table.Cell().Text("USB Connections");
                table.Cell().Text(reportData["UsbConnections"].ToString());
            });
            
            column.Spacing(10);
            
            // File Events Summary
            column.Item().Text("File Activity Summary").FontSize(14).SemiBold();
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(3);
                    columns.RelativeColumn(2);
                });
                
                table.Header(header =>
                {
                    header.Cell().Text("Event Type").SemiBold();
                    header.Cell().Text("Count").SemiBold();
                });
                
                var fileEvents = (Dictionary<string, long>)reportData["FileEvents"];
                foreach (var fileEvent in fileEvents)
                {
                    table.Cell().Text(fileEvent.Key);
                    table.Cell().Text(fileEvent.Value.ToString());
                }
            });
            
            column.Spacing(10);
            
            // Quarantined Files Summary
            column.Item().Text("Quarantined Files Summary").FontSize(14).SemiBold();
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(3);
                    columns.RelativeColumn(2);
                });
                
                table.Header(header =>
                {
                    header.Cell().Text("Category").SemiBold();
                    header.Cell().Text("Count").SemiBold();
                });
                
                table.Cell().Text("Total Quarantined Files");
                table.Cell().Text(reportData["QuarantinedFiles"].ToString());
            });
            
            column.Spacing(10);
            
            // User Activity Summary
            column.Item().Text("User Activity Summary").FontSize(14).SemiBold();
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(3);
                    columns.RelativeColumn(2);
                });
                
                table.Header(header =>
                {
                    header.Cell().Text("Username").SemiBold();
                    header.Cell().Text("File Operations").SemiBold();
                });
                
                var userActivity = (Dictionary<string, long>)reportData["UserActivity"];
                foreach (var user in userActivity)
                {
                    table.Cell().Text(user.Key);
                    table.Cell().Text(user.Value.ToString());
                }
            });
        });
    }

    private Dictionary<string, object> GetReportData(DateTime startDate, DateTime endDate)
    {
        var reportData = new Dictionary<string, object>();
        
        using var connection = new SqliteConnection(_connectionString);
        connection.Open();
        
        // Get USB connections count
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*) 
                FROM USBEvents 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate 
                AND EventType = 0"; // Connected events
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            reportData["UsbConnections"] = (long)command.ExecuteScalar();
        }
        
        // Get file events summary
        var fileEvents = new Dictionary<string, long>();
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT EventType, COUNT(*) 
                FROM FileEvents 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate 
                GROUP BY EventType";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var eventType = reader.GetInt32(0);
                var count = reader.GetInt64(1);
                var eventTypeName = eventType switch
                {
                    0 => "Files Created",
                    1 => "Files Modified",
                    2 => "Files Deleted",
                    3 => "Files Renamed",
                    _ => $"Unknown Event Type ({eventType})"
                };
                
                fileEvents[eventTypeName] = count;
            }
        }
        reportData["FileEvents"] = fileEvents;
        
        // Get quarantined files count
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*) 
                FROM QuarantinedFiles 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            reportData["QuarantinedFiles"] = (long)command.ExecuteScalar();
        }
        
        // Get user activity summary
        var userActivity = new Dictionary<string, long>();
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT Username, COUNT(*) 
                FROM FileEvents 
                WHERE Timestamp BETWEEN @StartDate AND @EndDate 
                GROUP BY Username";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("o"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("o"));
            
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var username = reader.GetString(0);
                var count = reader.GetInt64(1);
                
                userActivity[username] = count;
            }
        }
        reportData["UserActivity"] = userActivity;
        
        return reportData;
    }
}
