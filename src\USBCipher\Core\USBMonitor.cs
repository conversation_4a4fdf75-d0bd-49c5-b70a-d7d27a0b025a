using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using USBCipher.Core.Platform;
using USBCipher.Models;
using USBCipher.Services;

namespace USBCipher.Core;

/// <summary>
/// Core class for monitoring USB devices and file operations
/// </summary>
public class USBMonitor
{
    private readonly DatabaseService _databaseService;
    private readonly SensitiveDataDetector _sensitiveDataDetector;
    private readonly QuarantineService _quarantineService;
    private readonly NotificationService _notificationService;
    private readonly List<FileSystemWatcher> _watchers = new();
    private bool _isMonitoring = false;

    // Platform-specific monitors
    private WindowsUSBMonitor? _windowsMonitor;
    private MacOSUSBMonitor? _macOSMonitor;
    private LinuxUSBMonitor? _linuxMonitor;

    public USBMonitor(
        DatabaseService databaseService,
        SensitiveDataDetector sensitiveDataDetector,
        QuarantineService quarantineService,
        NotificationService notificationService)
    {
        _databaseService = databaseService;
        _sensitiveDataDetector = sensitiveDataDetector;
        _quarantineService = quarantineService;
        _notificationService = notificationService;
    }

    public void StartMonitoring()
    {
        if (_isMonitoring)
            return;

        _isMonitoring = true;

        try
        {
            // Register for USB device events based on the operating system
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                _windowsMonitor = new WindowsUSBMonitor(OnUSBDeviceConnected, OnUSBDeviceDisconnected);
                _windowsMonitor.Start();
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                _macOSMonitor = new MacOSUSBMonitor(OnUSBDeviceConnected, OnUSBDeviceDisconnected);
                _macOSMonitor.Start();
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                _linuxMonitor = new LinuxUSBMonitor(OnUSBDeviceConnected, OnUSBDeviceDisconnected);
                _linuxMonitor.Start();
            }
            else
            {
                Console.WriteLine("Unsupported operating system for USB monitoring");
            }

            Console.WriteLine("USB monitoring started");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting USB monitoring: {ex.Message}");
            _isMonitoring = false;
            throw;
        }
    }

    public void StopMonitoring()
    {
        if (!_isMonitoring)
            return;

        _isMonitoring = false;

        try
        {
            // Stop platform-specific monitors
            if (_windowsMonitor != null)
            {
                _windowsMonitor.Stop();
                _windowsMonitor = null;
            }

            if (_macOSMonitor != null)
            {
                _macOSMonitor.Stop();
                _macOSMonitor = null;
            }

            if (_linuxMonitor != null)
            {
                _linuxMonitor.Stop();
                _linuxMonitor = null;
            }

            // Unregister file system watchers
            foreach (var watcher in _watchers)
            {
                watcher.EnableRaisingEvents = false;
                watcher.Dispose();
            }

            _watchers.Clear();

            Console.WriteLine("USB monitoring stopped");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error stopping USB monitoring: {ex.Message}");
        }
    }

    private void OnUSBDeviceConnected(string drivePath)
    {
        Console.WriteLine($"USB device connected: {drivePath}");

        // Log the USB connection event
        _databaseService.LogUSBEvent(new USBEvent
        {
            EventType = USBEventType.Connected,
            DrivePath = drivePath,
            Timestamp = DateTime.Now,
            Username = Environment.UserName
        });

        // Start monitoring file operations on the USB drive
        MonitorDrive(drivePath);
    }

    private void OnUSBDeviceDisconnected(string drivePath)
    {
        Console.WriteLine($"USB device disconnected: {drivePath}");

        // Log the USB disconnection event
        _databaseService.LogUSBEvent(new USBEvent
        {
            EventType = USBEventType.Disconnected,
            DrivePath = drivePath,
            Timestamp = DateTime.Now,
            Username = Environment.UserName
        });

        // Stop monitoring file operations on the USB drive
        StopMonitoringDrive(drivePath);
    }

    private void MonitorDrive(string drivePath)
    {
        var watcher = new FileSystemWatcher(drivePath)
        {
            IncludeSubdirectories = true,
            NotifyFilter = NotifyFilters.FileName | NotifyFilters.DirectoryName | NotifyFilters.LastWrite
        };

        watcher.Created += OnFileCreated;
        watcher.Deleted += OnFileDeleted;
        watcher.Changed += OnFileChanged;
        watcher.Renamed += OnFileRenamed;

        watcher.EnableRaisingEvents = true;

        _watchers.Add(watcher);
    }

    private void StopMonitoringDrive(string drivePath)
    {
        var watchersToRemove = _watchers.FindAll(w => w.Path.StartsWith(drivePath));

        foreach (var watcher in watchersToRemove)
        {
            watcher.EnableRaisingEvents = false;
            watcher.Dispose();
            _watchers.Remove(watcher);
        }
    }

    private void OnFileCreated(object sender, FileSystemEventArgs e)
    {
        ProcessFileEvent(e.FullPath, FileEventType.Created);
    }

    private void OnFileDeleted(object sender, FileSystemEventArgs e)
    {
        ProcessFileEvent(e.FullPath, FileEventType.Deleted);
    }

    private void OnFileChanged(object sender, FileSystemEventArgs e)
    {
        ProcessFileEvent(e.FullPath, FileEventType.Modified);
    }

    private void OnFileRenamed(object sender, RenamedEventArgs e)
    {
        ProcessFileEvent(e.FullPath, FileEventType.Renamed, e.OldFullPath);
    }

    private void ProcessFileEvent(string filePath, FileEventType eventType, string? oldPath = null)
    {
        // Skip directories
        if (Directory.Exists(filePath))
            return;

        // Log the file event
        var fileEvent = new FileEvent
        {
            EventType = eventType,
            FilePath = filePath,
            OldFilePath = oldPath,
            Timestamp = DateTime.Now,
            Username = Environment.UserName,
            FileSize = eventType != FileEventType.Deleted ? new FileInfo(filePath).Length : 0,
            FileType = Path.GetExtension(filePath)
        };

        _databaseService.LogFileEvent(fileEvent);

        // Check for sensitive data if the file was created or modified
        if (eventType == FileEventType.Created || eventType == FileEventType.Modified)
        {
            CheckForSensitiveData(filePath);
        }
    }

    private void CheckForSensitiveData(string filePath)
    {
        try
        {
            var result = _sensitiveDataDetector.AnalyzeFile(filePath);

            if (result.ContainsSensitiveData)
            {
                // Quarantine the file
                _quarantineService.QuarantineFile(filePath, result.DetectionReason);

                // Send notification
                _notificationService.SendAlert(
                    $"Sensitive data detected in file: {Path.GetFileName(filePath)}",
                    $"User: {Environment.UserName}\nFile: {filePath}\nReason: {result.DetectionReason}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error checking file for sensitive data: {ex.Message}");
        }
    }
}
