using System;
using System.CommandLine;
using System.IO;
using System.Threading.Tasks;
using USBCipher.Core;
using USBCipher.Services;
using USBCipher.UI;

namespace USBCipher;

class Program
{
    private static USBMonitor? _usbMonitor;
    private static readonly string _configFilePath = GetConfigFilePath();
    private static readonly string _encryptionKey = "USBCipher_Secure_Key_2023"; // In production, this should be stored securely

    static async Task<int> Main(string[] args)
    {
        Console.WriteLine("USBCipher - USB Monitoring and Data Protection");

        var rootCommand = new RootCommand("USBCipher - USB Monitoring and Data Protection");

        var dashboardCommand = new Command("--dashboard", "Open the admin dashboard");
        dashboardCommand.SetHandler(() => RunDashboard());

        var startCommand = new Command("start", "Start the USB monitoring service");
        startCommand.SetHandler(() => StartMonitoring());

        var stopCommand = new Command("stop", "Stop the USB monitoring service");
        stopCommand.SetHandler(() => StopMonitoring());

        var statusCommand = new Command("status", "Check the status of the USB monitoring service");
        statusCommand.SetHandler(() => CheckStatus());

        rootCommand.AddCommand(dashboardCommand);
        rootCommand.AddCommand(startCommand);
        rootCommand.AddCommand(stopCommand);
        rootCommand.AddCommand(statusCommand);

        return await rootCommand.InvokeAsync(args);
    }

    private static void RunDashboard()
    {
        Console.WriteLine("Starting admin dashboard...");

        try
        {
            // Initialize services
            var configService = new ConfigurationService(_configFilePath, _encryptionKey);
            var config = configService.GetConfig();

            var databaseService = new DatabaseService(config.DatabasePath);
            var quarantineService = new QuarantineService(config.QuarantinePath, databaseService);
            var notificationService = new NotificationService(
                config.EmailSettings.SmtpServer,
                config.EmailSettings.SmtpPort,
                config.EmailSettings.SmtpUsername,
                config.EmailSettings.SmtpPassword,
                config.EmailSettings.FromEmail,
                config.EmailSettings.ToEmail,
                config.SlackSettings.WebhookUrl);
            var reportingService = new ReportingService(
                config.DatabasePath,
                config.ReportOutputPath,
                notificationService);

            // Create and run the dashboard
            var dashboard = new AdminDashboard(databaseService, quarantineService, reportingService);
            dashboard.Run();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting admin dashboard: {ex.Message}");
        }
    }

    private static void StartMonitoring()
    {
        Console.WriteLine("Starting USB monitoring service...");

        try
        {
            if (_usbMonitor != null)
            {
                Console.WriteLine("USB monitoring service is already running.");
                return;
            }

            // Initialize services
            var configService = new ConfigurationService(_configFilePath, _encryptionKey);
            var config = configService.GetConfig();

            var databaseService = new DatabaseService(config.DatabasePath);
            var sensitiveDataDetector = new SensitiveDataDetector(
                config.SensitiveDataSettings.SensitiveKeywords,
                config.SensitiveDataSettings.WatchedFileExtensions);
            var quarantineService = new QuarantineService(config.QuarantinePath, databaseService);
            var notificationService = new NotificationService(
                config.EmailSettings.SmtpServer,
                config.EmailSettings.SmtpPort,
                config.EmailSettings.SmtpUsername,
                config.EmailSettings.SmtpPassword,
                config.EmailSettings.FromEmail,
                config.EmailSettings.ToEmail,
                config.SlackSettings.WebhookUrl);

            // Create and start the USB monitor
            _usbMonitor = new USBMonitor(databaseService, sensitiveDataDetector, quarantineService, notificationService);
            _usbMonitor.StartMonitoring();

            Console.WriteLine("USB monitoring service started successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting USB monitoring service: {ex.Message}");
        }
    }

    private static void StopMonitoring()
    {
        Console.WriteLine("Stopping USB monitoring service...");

        try
        {
            if (_usbMonitor == null)
            {
                Console.WriteLine("USB monitoring service is not running.");
                return;
            }

            _usbMonitor.StopMonitoring();
            _usbMonitor = null;

            Console.WriteLine("USB monitoring service stopped successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error stopping USB monitoring service: {ex.Message}");
        }
    }

    private static void CheckStatus()
    {
        Console.WriteLine("Checking USB monitoring service status...");

        if (_usbMonitor == null)
        {
            Console.WriteLine("USB monitoring service is not running.");
        }
        else
        {
            Console.WriteLine("USB monitoring service is running.");
        }
    }

    private static string GetConfigFilePath()
    {
        string basePath;

        if (OperatingSystem.IsWindows())
        {
            basePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "USBCipher");
        }
        else if (OperatingSystem.IsMacOS())
        {
            basePath = "/Library/Application Support/USBCipher";
        }
        else // Linux
        {
            basePath = "/opt/usbcipher";
        }

        Directory.CreateDirectory(basePath);
        return Path.Combine(basePath, "usbcipher.config.json");
    }
}
