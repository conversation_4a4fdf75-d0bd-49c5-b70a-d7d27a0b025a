using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace USBCipher.Core.Platform;

/// <summary>
/// Linux-specific implementation for USB device monitoring
/// </summary>
public class LinuxUSBMonitor
{
    private readonly Action<string> _onDeviceConnected;
    private readonly Action<string> _onDeviceDisconnected;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _monitoringTask;
    private Process? _udevProcess;

    public LinuxUSBMonitor(Action<string> onDeviceConnected, Action<string> onDeviceDisconnected)
    {
        _onDeviceConnected = onDeviceConnected;
        _onDeviceDisconnected = onDeviceDisconnected;
    }

    public void Start()
    {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            throw new PlatformNotSupportedException("Linux USB monitor can only be used on Linux.");
        }

        try
        {
            // Start the udev monitor process
            _udevProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "udevadm",
                    Arguments = "monitor --udev --subsystem-match=block",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                }
            };
            
            _udevProcess.Start();
            
            // Start the monitoring task
            _cancellationTokenSource = new CancellationTokenSource();
            _monitoringTask = Task.Run(() => MonitorUdev(_cancellationTokenSource.Token));
            
            Console.WriteLine("Linux USB monitoring started");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting Linux USB monitoring: {ex.Message}");
            throw;
        }
    }

    public void Stop()
    {
        try
        {
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }
            
            if (_udevProcess != null)
            {
                if (!_udevProcess.HasExited)
                {
                    _udevProcess.Kill();
                }
                
                _udevProcess.Dispose();
                _udevProcess = null;
            }
            
            if (_monitoringTask != null)
            {
                _monitoringTask = null;
            }
            
            Console.WriteLine("Linux USB monitoring stopped");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error stopping Linux USB monitoring: {ex.Message}");
        }
    }

    private async Task MonitorUdev(CancellationToken cancellationToken)
    {
        if (_udevProcess == null)
        {
            return;
        }
        
        var addRegex = new Regex(@"UDEV\s+\[\d+\]\s+add\s+.*/(?<device>sd[a-z]\d*)");
        var removeRegex = new Regex(@"UDEV\s+\[\d+\]\s+remove\s+.*/(?<device>sd[a-z]\d*)");
        
        while (!cancellationToken.IsCancellationRequested && !_udevProcess.HasExited)
        {
            try
            {
                var line = await _udevProcess.StandardOutput.ReadLineAsync();
                
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }
                
                // Check for device addition
                var addMatch = addRegex.Match(line);
                if (addMatch.Success)
                {
                    var device = addMatch.Groups["device"].Value;
                    var mountPoint = GetMountPoint(device);
                    
                    if (!string.IsNullOrEmpty(mountPoint))
                    {
                        _onDeviceConnected(mountPoint);
                    }
                    
                    continue;
                }
                
                // Check for device removal
                var removeMatch = removeRegex.Match(line);
                if (removeMatch.Success)
                {
                    var device = removeMatch.Groups["device"].Value;
                    var mountPoint = GetMountPoint(device);
                    
                    if (!string.IsNullOrEmpty(mountPoint))
                    {
                        _onDeviceDisconnected(mountPoint);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error monitoring udev: {ex.Message}");
                await Task.Delay(1000, cancellationToken);
            }
        }
    }

    private string GetMountPoint(string device)
    {
        try
        {
            // Use the mount command to find the mount point
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "mount",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                }
            };
            
            process.Start();
            var output = process.StandardOutput.ReadToEnd();
            process.WaitForExit();
            
            // Parse the output to find the mount point
            var regex = new Regex($@"/dev/{device} on (?<mountpoint>[^\s]+)");
            var match = regex.Match(output);
            
            if (match.Success)
            {
                return match.Groups["mountpoint"].Value;
            }
            
            return string.Empty;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting mount point: {ex.Message}");
            return string.Empty;
        }
    }
}
