using System;
using System.Collections.Generic;
using System.Linq;
using USBCipher.Models;
using USBCipher.Services;

namespace USBCipher.UI;

/// <summary>
/// Terminal-based admin dashboard
/// </summary>
public class AdminDashboard
{
    private readonly DatabaseService _databaseService;
    private readonly QuarantineService _quarantineService;
    private readonly ReportingService _reportingService;
    
    private int _selectedMenuIndex = 0;
    private readonly string[] _menuItems = 
    {
        "View Recent Activity",
        "Search Logs",
        "Manage Quarantined Files",
        "Generate Report",
        "Exit"
    };

    public AdminDashboard(
        DatabaseService databaseService,
        QuarantineService quarantineService,
        ReportingService reportingService)
    {
        _databaseService = databaseService;
        _quarantineService = quarantineService;
        _reportingService = reportingService;
    }

    public void Run()
    {
        bool exit = false;
        
        while (!exit)
        {
            Console.Clear();
            DisplayHeader();
            DisplayMenu();
            
            var key = Console.ReadKey(true).Key;
            
            switch (key)
            {
                case ConsoleKey.UpArrow:
                    _selectedMenuIndex = Math.Max(0, _selectedMenuIndex - 1);
                    break;
                
                case ConsoleKey.DownArrow:
                    _selectedMenuIndex = Math.Min(_menuItems.Length - 1, _selectedMenuIndex + 1);
                    break;
                
                case ConsoleKey.Enter:
                    switch (_selectedMenuIndex)
                    {
                        case 0:
                            ViewRecentActivity();
                            break;
                        
                        case 1:
                            SearchLogs();
                            break;
                        
                        case 2:
                            ManageQuarantinedFiles();
                            break;
                        
                        case 3:
                            GenerateReport();
                            break;
                        
                        case 4:
                            exit = true;
                            break;
                    }
                    break;
            }
        }
    }

    private void DisplayHeader()
    {
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine("╔════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                USBCipher Admin Dashboard                   ║");
        Console.WriteLine("╚════════════════════════════════════════════════════════════╝");
        Console.ResetColor();
        Console.WriteLine();
    }

    private void DisplayMenu()
    {
        Console.WriteLine("Use arrow keys to navigate and Enter to select:");
        Console.WriteLine();
        
        for (int i = 0; i < _menuItems.Length; i++)
        {
            if (i == _selectedMenuIndex)
            {
                Console.ForegroundColor = ConsoleColor.Black;
                Console.BackgroundColor = ConsoleColor.White;
                Console.WriteLine($" > {_menuItems[i]}");
                Console.ResetColor();
            }
            else
            {
                Console.WriteLine($"   {_menuItems[i]}");
            }
        }
    }

    private void ViewRecentActivity()
    {
        Console.Clear();
        DisplayHeader();
        Console.WriteLine("Recent Activity (Last 50 Events)");
        Console.WriteLine();
        
        var events = _databaseService.GetRecentFileEvents(50);
        
        if (events.Count == 0)
        {
            Console.WriteLine("No recent activity found.");
        }
        else
        {
            Console.WriteLine("╔════════════════╦════════════════╦════════════════════════════════╦════════════════╗");
            Console.WriteLine("║ Timestamp      ║ User           ║ Event                          ║ File Type      ║");
            Console.WriteLine("╠════════════════╬════════════════╬════════════════════════════════╬════════════════╣");
            
            foreach (var evt in events)
            {
                var eventDescription = evt.EventType switch
                {
                    FileEventType.Created => $"Created: {TruncateString(Path.GetFileName(evt.FilePath), 20)}",
                    FileEventType.Modified => $"Modified: {TruncateString(Path.GetFileName(evt.FilePath), 20)}",
                    FileEventType.Deleted => $"Deleted: {TruncateString(Path.GetFileName(evt.FilePath), 20)}",
                    FileEventType.Renamed => $"Renamed: {TruncateString(Path.GetFileName(evt.FilePath), 20)}",
                    _ => $"Unknown: {TruncateString(Path.GetFileName(evt.FilePath), 20)}"
                };
                
                Console.WriteLine($"║ {evt.Timestamp:MM/dd HH:mm:ss} ║ {PadString(evt.Username, 14)} ║ {PadString(eventDescription, 30)} ║ {PadString(evt.FileType, 14)} ║");
            }
            
            Console.WriteLine("╚════════════════╩════════════════╩════════════════════════════════╩════════════════╝");
        }
        
        Console.WriteLine();
        Console.WriteLine("Press any key to return to the menu...");
        Console.ReadKey(true);
    }

    private void SearchLogs()
    {
        Console.Clear();
        DisplayHeader();
        Console.WriteLine("Search Logs");
        Console.WriteLine();
        
        Console.WriteLine("Search options:");
        Console.WriteLine("1. Search by username");
        Console.WriteLine("2. Search by date range");
        Console.WriteLine("3. Search by filename");
        Console.WriteLine("4. Return to main menu");
        Console.WriteLine();
        Console.Write("Enter your choice (1-4): ");
        
        var choice = Console.ReadKey(true).KeyChar;
        Console.WriteLine(choice);
        
        switch (choice)
        {
            case '1':
                SearchByUsername();
                break;
            
            case '2':
                SearchByDateRange();
                break;
            
            case '3':
                SearchByFilename();
                break;
            
            case '4':
                return;
            
            default:
                Console.WriteLine("Invalid choice. Press any key to try again...");
                Console.ReadKey(true);
                SearchLogs();
                break;
        }
    }

    private void SearchByUsername()
    {
        Console.WriteLine();
        Console.Write("Enter username to search for: ");
        var username = Console.ReadLine();
        
        Console.WriteLine();
        Console.WriteLine($"Search results for username: {username}");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private void SearchByDateRange()
    {
        Console.WriteLine();
        Console.WriteLine("Enter start date (MM/DD/YYYY): ");
        var startDateStr = Console.ReadLine();
        
        Console.WriteLine("Enter end date (MM/DD/YYYY): ");
        var endDateStr = Console.ReadLine();
        
        Console.WriteLine();
        Console.WriteLine($"Search results for date range: {startDateStr} to {endDateStr}");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private void SearchByFilename()
    {
        Console.WriteLine();
        Console.Write("Enter filename or part of filename to search for: ");
        var filename = Console.ReadLine();
        
        Console.WriteLine();
        Console.WriteLine($"Search results for filename: {filename}");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private void ManageQuarantinedFiles()
    {
        Console.Clear();
        DisplayHeader();
        Console.WriteLine("Manage Quarantined Files");
        Console.WriteLine();
        
        Console.WriteLine("Quarantined files management options:");
        Console.WriteLine("1. List quarantined files");
        Console.WriteLine("2. Release a quarantined file");
        Console.WriteLine("3. Delete a quarantined file");
        Console.WriteLine("4. Return to main menu");
        Console.WriteLine();
        Console.Write("Enter your choice (1-4): ");
        
        var choice = Console.ReadKey(true).KeyChar;
        Console.WriteLine(choice);
        
        switch (choice)
        {
            case '1':
                ListQuarantinedFiles();
                break;
            
            case '2':
                ReleaseQuarantinedFile();
                break;
            
            case '3':
                DeleteQuarantinedFile();
                break;
            
            case '4':
                return;
            
            default:
                Console.WriteLine("Invalid choice. Press any key to try again...");
                Console.ReadKey(true);
                ManageQuarantinedFiles();
                break;
        }
    }

    private void ListQuarantinedFiles()
    {
        Console.WriteLine();
        Console.WriteLine("List of quarantined files:");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private void ReleaseQuarantinedFile()
    {
        Console.WriteLine();
        Console.Write("Enter the ID of the quarantined file to release: ");
        var idStr = Console.ReadLine();
        
        Console.WriteLine();
        Console.WriteLine($"Releasing quarantined file with ID: {idStr}");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private void DeleteQuarantinedFile()
    {
        Console.WriteLine();
        Console.Write("Enter the ID of the quarantined file to delete: ");
        var idStr = Console.ReadLine();
        
        Console.WriteLine();
        Console.WriteLine($"Deleting quarantined file with ID: {idStr}");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private void GenerateReport()
    {
        Console.Clear();
        DisplayHeader();
        Console.WriteLine("Generate Report");
        Console.WriteLine();
        
        Console.WriteLine("Report generation options:");
        Console.WriteLine("1. Generate weekly report");
        Console.WriteLine("2. Generate custom date range report");
        Console.WriteLine("3. Return to main menu");
        Console.WriteLine();
        Console.Write("Enter your choice (1-3): ");
        
        var choice = Console.ReadKey(true).KeyChar;
        Console.WriteLine(choice);
        
        switch (choice)
        {
            case '1':
                GenerateWeeklyReport();
                break;
            
            case '2':
                GenerateCustomReport();
                break;
            
            case '3':
                return;
            
            default:
                Console.WriteLine("Invalid choice. Press any key to try again...");
                Console.ReadKey(true);
                GenerateReport();
                break;
        }
    }

    private void GenerateWeeklyReport()
    {
        Console.WriteLine();
        Console.WriteLine("Generating weekly report...");
        
        // Call the reporting service to generate the report
        _reportingService.GenerateWeeklyReport().Wait();
        
        Console.WriteLine("Weekly report generated successfully.");
        Console.WriteLine("Press any key to return...");
        Console.ReadKey(true);
    }

    private void GenerateCustomReport()
    {
        Console.WriteLine();
        Console.WriteLine("Enter start date (MM/DD/YYYY): ");
        var startDateStr = Console.ReadLine();
        
        Console.WriteLine("Enter end date (MM/DD/YYYY): ");
        var endDateStr = Console.ReadLine();
        
        Console.WriteLine();
        Console.WriteLine($"Generating report for date range: {startDateStr} to {endDateStr}");
        Console.WriteLine("Not implemented yet. Press any key to return...");
        Console.ReadKey(true);
    }

    private string TruncateString(string str, int maxLength)
    {
        if (string.IsNullOrEmpty(str))
            return string.Empty;
            
        return str.Length <= maxLength ? str : str.Substring(0, maxLength - 3) + "...";
    }

    private string PadString(string str, int length)
    {
        if (string.IsNullOrEmpty(str))
            return new string(' ', length);
            
        if (str.Length > length)
            return str.Substring(0, length);
            
        return str + new string(' ', length - str.Length);
    }
}
