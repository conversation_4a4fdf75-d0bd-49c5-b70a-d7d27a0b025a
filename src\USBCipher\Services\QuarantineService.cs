using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.AccessControl;
using System.Security.Principal;

namespace USBCipher.Services;

/// <summary>
/// Service for quarantining files with sensitive data
/// </summary>
public class QuarantineService
{
    private readonly string _quarantinePath;
    private readonly DatabaseService _databaseService;

    public QuarantineService(string quarantinePath, DatabaseService databaseService)
    {
        _quarantinePath = quarantinePath;
        _databaseService = databaseService;
        
        // Ensure the quarantine directory exists
        Directory.CreateDirectory(_quarantinePath);
        
        // Set appropriate permissions on the quarantine directory
        SetQuarantineDirectoryPermissions();
    }

    public string QuarantineFile(string filePath, string detectionReason)
    {
        try
        {
            // Generate a unique filename for the quarantined file
            var fileName = Path.GetFileName(filePath);
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var quarantineFileName = $"{timestamp}_{fileName}";
            var quarantineFilePath = Path.Combine(_quarantinePath, quarantineFileName);
            
            // Copy the file to the quarantine directory
            File.Copy(filePath, quarantineFilePath, true);
            
            // Set appropriate permissions on the quarantined file
            SetQuarantineFilePermissions(quarantineFilePath);
            
            // Log the quarantined file
            _databaseService.LogQuarantinedFile(filePath, quarantineFilePath, Environment.UserName, detectionReason);
            
            // Delete the original file
            File.Delete(filePath);
            
            Console.WriteLine($"File quarantined: {filePath} -> {quarantineFilePath}");
            
            return quarantineFilePath;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error quarantining file {filePath}: {ex.Message}");
            throw;
        }
    }

    private void SetQuarantineDirectoryPermissions()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            try
            {
                // Get the current ACL
                var dirInfo = new DirectoryInfo(_quarantinePath);
                var dirSecurity = dirInfo.GetAccessControl();
                
                // Remove inheritance
                dirSecurity.SetAccessRuleProtection(true, false);
                
                // Add rule for administrators
                var adminSid = new SecurityIdentifier(WellKnownSidType.BuiltinAdministratorsSid, null);
                var adminRule = new FileSystemAccessRule(
                    adminSid,
                    FileSystemRights.FullControl,
                    InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit,
                    PropagationFlags.None,
                    AccessControlType.Allow);
                
                dirSecurity.AddAccessRule(adminRule);
                
                // Set the new ACL
                dirInfo.SetAccessControl(dirSecurity);
                
                Console.WriteLine("Quarantine directory permissions set successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting quarantine directory permissions: {ex.Message}");
            }
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) || 
                 RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            // For Linux and macOS, we would use chmod/chown via Process.Start
            // This is a simplified example and would need to be expanded for production use
            try
            {
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "chmod",
                        Arguments = $"700 {_quarantinePath}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };
                
                process.Start();
                process.WaitForExit();
                
                Console.WriteLine("Quarantine directory permissions set successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting quarantine directory permissions: {ex.Message}");
            }
        }
    }

    private void SetQuarantineFilePermissions(string filePath)
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            try
            {
                // Get the current ACL
                var fileInfo = new FileInfo(filePath);
                var fileSecurity = fileInfo.GetAccessControl();
                
                // Remove inheritance
                fileSecurity.SetAccessRuleProtection(true, false);
                
                // Add rule for administrators
                var adminSid = new SecurityIdentifier(WellKnownSidType.BuiltinAdministratorsSid, null);
                var adminRule = new FileSystemAccessRule(
                    adminSid,
                    FileSystemRights.FullControl,
                    InheritanceFlags.None,
                    PropagationFlags.None,
                    AccessControlType.Allow);
                
                fileSecurity.AddAccessRule(adminRule);
                
                // Set the new ACL
                fileInfo.SetAccessControl(fileSecurity);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting quarantine file permissions: {ex.Message}");
            }
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) || 
                 RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            // For Linux and macOS, we would use chmod via Process.Start
            try
            {
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "chmod",
                        Arguments = $"600 {filePath}",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };
                
                process.Start();
                process.WaitForExit();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting quarantine file permissions: {ex.Message}");
            }
        }
    }
}
