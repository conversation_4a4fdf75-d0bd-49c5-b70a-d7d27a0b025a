using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace USBCipher.Services;

/// <summary>
/// Service for managing application configuration
/// </summary>
public class ConfigurationService
{
    private readonly string _configFilePath;
    private readonly string? _encryptionKey;
    private AppConfig _config;

    public ConfigurationService(string configFilePath, string? encryptionKey = null)
    {
        _configFilePath = configFilePath;
        _encryptionKey = encryptionKey;
        
        // Load or create default configuration
        _config = LoadConfiguration();
    }

    public AppConfig GetConfig()
    {
        return _config;
    }

    public void SaveConfig()
    {
        try
        {
            var configJson = JsonSerializer.Serialize(_config, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            
            // Encrypt the configuration if an encryption key is provided
            if (!string.IsNullOrEmpty(_encryptionKey))
            {
                var encryptedConfig = EncryptString(configJson, _encryptionKey);
                File.WriteAllText(_configFilePath, encryptedConfig);
            }
            else
            {
                File.WriteAllText(_configFilePath, configJson);
            }
            
            Console.WriteLine("Configuration saved successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving configuration: {ex.Message}");
        }
    }

    private AppConfig LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configFilePath))
            {
                var configJson = File.ReadAllText(_configFilePath);
                
                // Decrypt the configuration if an encryption key is provided
                if (!string.IsNullOrEmpty(_encryptionKey))
                {
                    configJson = DecryptString(configJson, _encryptionKey);
                }
                
                var config = JsonSerializer.Deserialize<AppConfig>(configJson);
                
                if (config != null)
                {
                    Console.WriteLine("Configuration loaded successfully");
                    return config;
                }
            }
            
            // Create default configuration if file doesn't exist or deserialization failed
            Console.WriteLine("Creating default configuration");
            return CreateDefaultConfig();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading configuration: {ex.Message}");
            return CreateDefaultConfig();
        }
    }

    private AppConfig CreateDefaultConfig()
    {
        var config = new AppConfig
        {
            DatabasePath = GetDefaultDatabasePath(),
            QuarantinePath = GetDefaultQuarantinePath(),
            ReportOutputPath = GetDefaultReportOutputPath(),
            EmailSettings = new EmailSettings
            {
                SmtpServer = "smtp.example.com",
                SmtpPort = 587,
                SmtpUsername = "username",
                SmtpPassword = "password",
                FromEmail = "<EMAIL>",
                ToEmail = "<EMAIL>"
            },
            SlackSettings = new SlackSettings
            {
                WebhookUrl = "https://hooks.slack.com/services/your/webhook/url"
            },
            SensitiveDataSettings = new SensitiveDataSettings
            {
                SensitiveKeywords = new List<string>
                {
                    "confidential",
                    "secret",
                    "private",
                    "sensitive",
                    "classified",
                    "restricted",
                    "internal",
                    "proprietary",
                    "client",
                    "customer",
                    "password",
                    "credential"
                },
                WatchedFileExtensions = new List<string>
                {
                    ".docx",
                    ".xlsx",
                    ".pdf",
                    ".zip",
                    ".txt",
                    ".csv",
                    ".json",
                    ".xml"
                }
            }
        };
        
        // Save the default configuration
        _config = config;
        SaveConfig();
        
        return config;
    }

    private string GetDefaultDatabasePath()
    {
        string basePath;
        
        if (OperatingSystem.IsWindows())
        {
            basePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "USBCipher");
        }
        else if (OperatingSystem.IsMacOS())
        {
            basePath = "/Library/Application Support/USBCipher";
        }
        else // Linux
        {
            basePath = "/opt/usbcipher";
        }
        
        Directory.CreateDirectory(basePath);
        return Path.Combine(basePath, "usbcipher.db");
    }

    private string GetDefaultQuarantinePath()
    {
        string basePath;
        
        if (OperatingSystem.IsWindows())
        {
            basePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "USBCipher");
        }
        else if (OperatingSystem.IsMacOS())
        {
            basePath = "/Library/Application Support/USBCipher";
        }
        else // Linux
        {
            basePath = "/opt/usbcipher";
        }
        
        var quarantinePath = Path.Combine(basePath, "quarantine");
        Directory.CreateDirectory(quarantinePath);
        return quarantinePath;
    }

    private string GetDefaultReportOutputPath()
    {
        string basePath;
        
        if (OperatingSystem.IsWindows())
        {
            basePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "USBCipher");
        }
        else if (OperatingSystem.IsMacOS())
        {
            basePath = "/Library/Application Support/USBCipher";
        }
        else // Linux
        {
            basePath = "/opt/usbcipher";
        }
        
        var reportsPath = Path.Combine(basePath, "reports");
        Directory.CreateDirectory(reportsPath);
        return reportsPath;
    }

    private string EncryptString(string plainText, string key)
    {
        byte[] iv = new byte[16];
        byte[] array;
        
        using (Aes aes = Aes.Create())
        {
            aes.Key = Encoding.UTF8.GetBytes(key);
            aes.IV = iv;
            
            ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);
            
            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (CryptoStream cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                {
                    using (StreamWriter streamWriter = new StreamWriter(cryptoStream))
                    {
                        streamWriter.Write(plainText);
                    }
                    
                    array = memoryStream.ToArray();
                }
            }
        }
        
        return Convert.ToBase64String(array);
    }

    private string DecryptString(string cipherText, string key)
    {
        byte[] iv = new byte[16];
        byte[] buffer = Convert.FromBase64String(cipherText);
        
        using (Aes aes = Aes.Create())
        {
            aes.Key = Encoding.UTF8.GetBytes(key);
            aes.IV = iv;
            
            ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
            
            using (MemoryStream memoryStream = new MemoryStream(buffer))
            {
                using (CryptoStream cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                {
                    using (StreamReader streamReader = new StreamReader(cryptoStream))
                    {
                        return streamReader.ReadToEnd();
                    }
                }
            }
        }
    }
}

public class AppConfig
{
    public string DatabasePath { get; set; } = string.Empty;
    public string QuarantinePath { get; set; } = string.Empty;
    public string ReportOutputPath { get; set; } = string.Empty;
    public EmailSettings EmailSettings { get; set; } = new EmailSettings();
    public SlackSettings SlackSettings { get; set; } = new SlackSettings();
    public SensitiveDataSettings SensitiveDataSettings { get; set; } = new SensitiveDataSettings();
}

public class EmailSettings
{
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; } = 587;
    public string SmtpUsername { get; set; } = string.Empty;
    public string SmtpPassword { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string ToEmail { get; set; } = string.Empty;
}

public class SlackSettings
{
    public string WebhookUrl { get; set; } = string.Empty;
}

public class SensitiveDataSettings
{
    public List<string> SensitiveKeywords { get; set; } = new List<string>();
    public List<string> WatchedFileExtensions { get; set; } = new List<string>();
}
