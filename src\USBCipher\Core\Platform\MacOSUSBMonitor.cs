using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace USBCipher.Core.Platform;

/// <summary>
/// macOS-specific implementation for USB device monitoring
/// </summary>
public class MacOSUSBMonitor
{
    private readonly Action<string> _onDeviceConnected;
    private readonly Action<string> _onDeviceDisconnected;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _monitoringTask;
    private readonly string _volumesPath = "/Volumes";
    private readonly HashSet<string> _currentVolumes = new();

    public MacOSUSBMonitor(Action<string> onDeviceConnected, Action<string> onDeviceDisconnected)
    {
        _onDeviceConnected = onDeviceConnected;
        _onDeviceDisconnected = onDeviceDisconnected;
    }

    public void Start()
    {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            throw new PlatformNotSupportedException("macOS USB monitor can only be used on macOS.");
        }

        try
        {
            // Initialize the current volumes
            _currentVolumes.Clear();
            foreach (var volume in Directory.GetDirectories(_volumesPath))
            {
                if (IsRemovableDrive(volume))
                {
                    _currentVolumes.Add(volume);
                }
            }
            
            // Start the monitoring task
            _cancellationTokenSource = new CancellationTokenSource();
            _monitoringTask = Task.Run(() => MonitorVolumes(_cancellationTokenSource.Token));
            
            Console.WriteLine("macOS USB monitoring started");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting macOS USB monitoring: {ex.Message}");
            throw;
        }
    }

    public void Stop()
    {
        try
        {
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }
            
            if (_monitoringTask != null)
            {
                _monitoringTask = null;
            }
            
            Console.WriteLine("macOS USB monitoring stopped");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error stopping macOS USB monitoring: {ex.Message}");
        }
    }

    private async Task MonitorVolumes(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var currentVolumes = new HashSet<string>();
                
                foreach (var volume in Directory.GetDirectories(_volumesPath))
                {
                    if (IsRemovableDrive(volume))
                    {
                        currentVolumes.Add(volume);
                        
                        // Check for new volumes
                        if (!_currentVolumes.Contains(volume))
                        {
                            _onDeviceConnected(volume);
                        }
                    }
                }
                
                // Check for removed volumes
                foreach (var volume in _currentVolumes)
                {
                    if (!currentVolumes.Contains(volume))
                    {
                        _onDeviceDisconnected(volume);
                    }
                }
                
                // Update the current volumes
                _currentVolumes.Clear();
                foreach (var volume in currentVolumes)
                {
                    _currentVolumes.Add(volume);
                }
                
                // Wait before checking again
                await Task.Delay(1000, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error monitoring volumes: {ex.Message}");
                await Task.Delay(5000, cancellationToken);
            }
        }
    }

    private bool IsRemovableDrive(string path)
    {
        try
        {
            // Use diskutil to check if the volume is removable
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "diskutil",
                    Arguments = $"info {path}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                }
            };
            
            process.Start();
            var output = process.StandardOutput.ReadToEnd();
            process.WaitForExit();
            
            // Check if the output contains indicators of a removable drive
            return output.Contains("Removable Media") && output.Contains("Yes") ||
                   output.Contains("USB") ||
                   output.Contains("External");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error checking if drive is removable: {ex.Message}");
            return false;
        }
    }
}
