# USBCipher

USBCipher is a cross-platform C# application designed to monitor USB file transfers and enforce data privacy on organizational laptops. The system runs silently in the background, provides real-time alerts, and supports administrative review and policy enforcement.

## Features

- **Real-Time USB Monitoring**: Detect USB insertion/removal and monitor file system changes on mounted USB volumes.
- **Sensitive Data Detection**: Analyze content using regex patterns, keyword lists, and watch listed file types.
- **Quarantine System**: Automatically quarantine files containing sensitive data.
- **Alerts & Notifications**: Send real-time alerts via email and Slack when sensitive data is detected.
- **Automated Weekly Reports**: Generate and email reports in both CSV and PDF formats.
- **Admin Terminal Dashboard**: Command-line interface for administrators to view logs, search events, and manage quarantined files.

## Requirements

- .NET 8 SDK
- Windows, macOS, or Linux operating system
- Administrator privileges for installation and running

## Building from Source

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/USBCipher.git
   cd USBCipher
   ```

2. Build the solution:
   ```
   dotnet build
   ```

3. Publish for your target platform:

   **Windows**:
   ```
   dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
   ```

   **macOS**:
   ```
   dotnet publish -c Release -r osx-x64 --self-contained true -p:PublishSingleFile=true
   ```

   **Linux**:
   ```
   dotnet publish -c Release -r linux-x64 --self-contained true -p:PublishSingleFile=true
   ```

## Installation

### Windows

1. Copy the published executable to a secure location (e.g., `C:\Program Files\USBCipher\`).
2. Run the executable with administrator privileges to initialize the configuration.
3. Set up the service to run at startup:
   ```
   sc create USBCipher binPath= "C:\Program Files\USBCipher\USBCipher.exe start" start= auto
   sc description USBCipher "USB Monitoring and Data Protection Service"
   ```

### macOS

1. Copy the published executable to `/usr/local/bin/usbcipher`.
2. Create a LaunchDaemon to run the service at startup:
   ```
   sudo nano /Library/LaunchDaemons/com.yourdomain.usbcipher.plist
   ```
   
   Add the following content:
   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
       <key>Label</key>
       <string>com.yourdomain.usbcipher</string>
       <key>ProgramArguments</key>
       <array>
           <string>/usr/local/bin/usbcipher</string>
           <string>start</string>
       </array>
       <key>RunAtLoad</key>
       <true/>
       <key>KeepAlive</key>
       <true/>
   </dict>
   </plist>
   ```

3. Load the LaunchDaemon:
   ```
   sudo launchctl load /Library/LaunchDaemons/com.yourdomain.usbcipher.plist
   ```

### Linux

1. Copy the published executable to `/usr/local/bin/usbcipher`.
2. Create a systemd service:
   ```
   sudo nano /etc/systemd/system/usbcipher.service
   ```
   
   Add the following content:
   ```
   [Unit]
   Description=USB Monitoring and Data Protection Service
   After=network.target

   [Service]
   ExecStart=/usr/local/bin/usbcipher start
   Restart=always
   User=root
   Group=root

   [Install]
   WantedBy=multi-user.target
   ```

3. Enable and start the service:
   ```
   sudo systemctl enable usbcipher
   sudo systemctl start usbcipher
   ```

## Configuration

The configuration file is located at:

- Windows: `C:\ProgramData\USBCipher\usbcipher.config.json`
- macOS: `/Library/Application Support/USBCipher/usbcipher.config.json`
- Linux: `/opt/usbcipher/usbcipher.config.json`

The configuration file contains settings for:

- Database path
- Quarantine folder path
- Report output path
- Email settings (SMTP server, credentials, etc.)
- Slack webhook URL
- Sensitive data detection settings (keywords, file extensions)

## Usage

### Starting the Service

```
usbcipher start
```

### Stopping the Service

```
usbcipher stop
```

### Checking Service Status

```
usbcipher status
```

### Opening the Admin Dashboard

```
usbcipher --dashboard
```

## Security Considerations

- The configuration file is encrypted to protect sensitive information like SMTP credentials.
- The quarantine folder has restricted permissions to prevent unauthorized access.
- The application requires administrator privileges to run properly.

## Dependencies

- SQLite for local storage
- MailKit for email notifications
- QuestPDF for PDF report generation
- System.CommandLine for command-line interface

## License

[MIT License](LICENSE)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
